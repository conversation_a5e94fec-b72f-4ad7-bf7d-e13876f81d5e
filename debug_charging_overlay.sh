#!/bin/bash

# Debug script for charging overlay UI bug investigation
# This script helps monitor the charging overlay service and related components

echo "=== CHARGING OVERLAY DEBUG SCRIPT ==="
echo "This script will help debug the charging overlay UI bug"
echo ""

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  logs     - Show real-time logs for charging overlay components"
    echo "  state    - Show current app state and configuration"
    echo "  test     - Simulate charging events for testing"
    echo "  clear    - Clear logcat buffer"
    echo "  help     - Show this help message"
    echo ""
}

# Function to show real-time logs
show_logs() {
    echo "=== MONITORING CHARGING OVERLAY LOGS ==="
    echo "Press Ctrl+C to stop monitoring"
    echo ""
    
    # Monitor specific log tags related to charging overlay
    adb logcat -c  # Clear existing logs
    adb logcat | grep -E "(CHARGING_OVERLAY|ANIMATION_APPLY|VIDEO_DOWNLOAD|BATTERY_SERVICE|OVERLAY_TRIGGER|BatteryManager|ChargingOverlayService|ChargingOverlayActivity)"
}

# Function to show current state
show_state() {
    echo "=== CURRENT APP STATE ==="
    echo ""
    
    echo "1. Checking if ChargingOverlayService is running..."
    adb shell "ps | grep com.tqhit.battery.one" || echo "App not running"
    echo ""
    
    echo "2. Checking battery status..."
    adb shell dumpsys battery
    echo ""
    
    echo "3. Checking app data directory..."
    adb shell "ls -la /data/data/com.tqhit.battery.one/files/" 2>/dev/null || echo "Cannot access app data directory (requires root)"
    echo ""
    
    echo "4. Checking for video files..."
    adb shell "find /data/data/com.tqhit.battery.one/files/ -name '*.mp4' -ls" 2>/dev/null || echo "Cannot access app files (requires root)"
    echo ""
}

# Function to test charging events
test_charging() {
    echo "=== TESTING CHARGING EVENTS ==="
    echo ""
    
    echo "1. Simulating charger unplug..."
    adb shell dumpsys battery unplug
    sleep 2
    
    echo "2. Simulating charger plug..."
    adb shell dumpsys battery set ac 1
    sleep 2
    
    echo "3. Resetting battery simulation..."
    adb shell dumpsys battery reset
    
    echo ""
    echo "Test completed. Check logs for overlay activity."
}

# Function to clear logs
clear_logs() {
    echo "Clearing logcat buffer..."
    adb logcat -c
    echo "Logcat buffer cleared."
}

# Main script logic
case "${1:-help}" in
    "logs")
        show_logs
        ;;
    "state")
        show_state
        ;;
    "test")
        test_charging
        ;;
    "clear")
        clear_logs
        ;;
    "help"|*)
        show_usage
        ;;
esac
