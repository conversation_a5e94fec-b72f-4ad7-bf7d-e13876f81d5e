# Charging Overlay UI Bug Investigation Test Plan

## Bug Description
After completing Animation Charging setup, when user unplugs and then plugs the charger back into the device, the Overlay UI fails to appear.

## Root Cause Hypothesis
Based on code analysis, the issue is likely related to the change from static filename (`charging_animation.mp4`) to dynamic filenames based on URL. The ChargingOverlayService may not be finding the correct video file path.

## Test Steps

### 1. Setup and Initial State Check
```bash
# Clear logs and check initial state
./debug_charging_overlay.sh clear
./debug_charging_overlay.sh state
```

### 2. Apply Animation and Monitor Logs
```bash
# Start log monitoring in one terminal
./debug_charging_overlay.sh logs

# In another terminal or manually on device:
# 1. Open the app
# 2. Go to Animation section
# 3. Select and apply an animation
# 4. Complete the setup process
```

### 3. Test Charging Overlay Trigger
```bash
# Test charging events
./debug_charging_overlay.sh test

# Or manually:
# 1. Unplug charger from device
# 2. Wait 2-3 seconds
# 3. Plug charger back in
# 4. Check if overlay appears
```

### 4. Log Analysis Points
Monitor logs for these key components:

#### CHARGING_OVERLAY logs:
- Service startup and configuration
- Battery receiver triggers
- Video source determination
- Overlay activity launch attempts

#### ANIMATION_APPLY logs:
- Video URL and filename extraction
- Repository storage operations
- Background download process
- File verification results

#### VIDEO_DOWNLOAD logs:
- Download method selection (cache/preload/network)
- File copy operations
- Final file verification

## Expected Log Flow (Working Scenario)
1. `ANIMATION_APPLY: Animation application started`
2. `ANIMATION_APPLY: Saved to repository - Path: /data/data/.../filename.mp4`
3. `VIDEO_DOWNLOAD: Enhanced video download started`
4. `VIDEO_DOWNLOAD: Download completed successfully`
5. `CHARGING_OVERLAY: Service started`
6. `CHARGING_OVERLAY: Battery receiver triggered`
7. `CHARGING_OVERLAY: Charger plugged in - attempting to start overlay`
8. `CHARGING_OVERLAY: Determined video source: /path/to/file.mp4`
9. `CHARGING_OVERLAY: Starting ChargingOverlayActivity`

## Potential Issues to Look For
1. **File Path Mismatch**: Repository stores one path, but file is saved to different location
2. **File Not Found**: Video file doesn't exist at expected path
3. **Permission Issues**: Service can't access the video file
4. **Trial Expiration**: Animation trial has expired
5. **Overlay Disabled**: Animation overlay setting is disabled

## Debug Commands

### Check App State
```bash
# Check if service is running
adb shell "ps | grep com.tqhit.battery.one"

# Check app files (requires root)
adb shell "ls -la /data/data/com.tqhit.battery.one/files/"

# Check for video files
adb shell "find /data/data/com.tqhit.battery.one/files/ -name '*.mp4' -ls"
```

### Monitor Specific Logs
```bash
# Monitor charging overlay logs
adb logcat | grep -E "CHARGING_OVERLAY"

# Monitor animation application logs
adb logcat | grep -E "ANIMATION_APPLY"

# Monitor video download logs
adb logcat | grep -E "VIDEO_DOWNLOAD"

# Monitor all related logs
adb logcat | grep -E "(CHARGING_OVERLAY|ANIMATION_APPLY|VIDEO_DOWNLOAD|BatteryManager)"
```

### Simulate Battery Events
```bash
# Unplug charger
adb shell dumpsys battery unplug

# Plug charger
adb shell dumpsys battery set ac 1

# Reset battery simulation
adb shell dumpsys battery reset
```

## Success Criteria
- Overlay UI appears when charger is plugged in after initial setup
- Logs show successful video source determination
- No file not found errors in logs
- Service properly detects charging state changes
