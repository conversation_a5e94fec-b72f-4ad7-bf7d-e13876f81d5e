package com.tqhit.battery.one.service

import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import android.os.IBinder
import android.util.Log
import androidx.core.content.ContextCompat
import com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity
import com.tqhit.battery.one.repository.AnimationRepository
import com.tqhit.battery.one.repository.AppRepository
import com.tqhit.battery.one.utils.BatteryLogger
import com.tqhit.battery.one.utils.NotificationUtils
import dagger.hilt.android.AndroidEntryPoint
import java.io.File
import javax.inject.Inject

@AndroidEntryPoint
class ChargingOverlayService : Service() {
    @Inject lateinit var appRepository: AppRepository
    @Inject lateinit var animationRepository: AnimationRepository
    private var isCharging = false
    private var videoPath: String? = null

    private val batteryReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent?) {
            BatteryLogger.d(TAG, "Battery receiver triggered")
            val status = intent?.getIntExtra(BatteryManager.EXTRA_STATUS, -1) ?: -1
            val charging = status == BatteryManager.BATTERY_STATUS_CHARGING || status == BatteryManager.BATTERY_STATUS_FULL

            BatteryLogger.d(TAG, "Battery status: $status, charging: $charging, isCharging: $isCharging")

            if (charging && !isCharging) {
                BatteryLogger.i(TAG, "Charger plugged in - attempting to start overlay")
                isCharging = true
                startOverlayActivity(context, intent)
            } else if (!charging && isCharging) {
                BatteryLogger.i(TAG, "Charger unplugged")
                isCharging = false
            }
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        BatteryLogger.i(TAG, "ChargingOverlayService started")

        NotificationUtils.createNotificationChannel(this)
        startForeground(NOTIFICATION_ID, NotificationUtils.createChargingOverlayNotification(this))
        registerReceiver(batteryReceiver, IntentFilter(Intent.ACTION_BATTERY_CHANGED))

        // Check current state immediately
        val batteryStatus = registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
        val status = batteryStatus?.getIntExtra(BatteryManager.EXTRA_STATUS, -1) ?: -1
        isCharging = status == BatteryManager.BATTERY_STATUS_CHARGING || status == BatteryManager.BATTERY_STATUS_FULL

        BatteryLogger.d(TAG, "Initial battery status: $status, isCharging: $isCharging")

        // Log current configuration state
        logCurrentState()

        return START_STICKY
    }

    private fun startOverlayActivity(context: Context, intent: Intent?) {
        BatteryLogger.d(TAG, "startOverlayActivity called")

        // Check if overlay is enabled
        val overlayEnabled = appRepository.isAnimationOverlayEnabled()
        BatteryLogger.d(TAG, "Animation overlay enabled: $overlayEnabled")

        if (!overlayEnabled) {
            BatteryLogger.w(TAG, "Overlay not enabled - skipping overlay display")
            return
        }

        // Check trial time
        val trialEndTime = animationRepository.getTrialEndTime()
        val currentTime = System.currentTimeMillis()
        BatteryLogger.d(TAG, "Trial end time: $trialEndTime, current time: $currentTime")

        if (trialEndTime <= currentTime) {
            BatteryLogger.w(TAG, "Trial expired - skipping overlay display")
            return
        }

        // Get video path and validate
        videoPath = determineVideoSource()
        BatteryLogger.i(TAG, "Determined video source: $videoPath")

        if (videoPath.isNullOrEmpty()) {
            BatteryLogger.e(TAG, "No valid video source found - cannot display overlay")
            return
        }

        // Create and start overlay activity
        val overlayIntent = Intent(this, ChargingOverlayActivity::class.java).apply {
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            putExtra("extra_video_path", videoPath)
        }

        try {
            BatteryLogger.i(TAG, "Starting ChargingOverlayActivity with video: $videoPath")
            context.startActivity(overlayIntent)
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Failed to start overlay activity", e)
        }
    }

    /**
     * Determines the best video source using the same priority logic as ChargingOverlayActivity
     */
    private fun determineVideoSource(): String? {
        BatteryLogger.d(TAG, "Determining video source...")

        // First priority: Check if permanent file exists
        val permanentFilePath = appRepository.getVideoPath()
        BatteryLogger.d(TAG, "Permanent file path from repository: $permanentFilePath")

        if (!permanentFilePath.isNullOrEmpty()) {
            val file = File(permanentFilePath)
            val exists = file.exists()
            val size = if (exists) file.length() else 0
            BatteryLogger.d(TAG, "Permanent file exists: $exists, size: $size bytes")

            if (exists && size > 0) {
                BatteryLogger.i(TAG, "Using permanent file: $permanentFilePath")
                return permanentFilePath
            }
        }

        // Second priority: Fall back to applied animation URL for immediate playback
        val appliedAnimationUrl = appRepository.getAppliedAnimationUrl()
        BatteryLogger.d(TAG, "Applied animation URL: $appliedAnimationUrl")

        if (!appliedAnimationUrl.isNullOrEmpty()) {
            BatteryLogger.i(TAG, "Using applied animation URL: $appliedAnimationUrl")
            return appliedAnimationUrl
        }

        BatteryLogger.w(TAG, "No valid video source found")
        return null
    }

    /**
     * Logs the current state for debugging
     */
    private fun logCurrentState() {
        BatteryLogger.d(TAG, "=== CHARGING OVERLAY SERVICE STATE ===")
        BatteryLogger.d(TAG, "Overlay enabled: ${appRepository.isAnimationOverlayEnabled()}")
        BatteryLogger.d(TAG, "Trial end time: ${animationRepository.getTrialEndTime()}")
        BatteryLogger.d(TAG, "Current time: ${System.currentTimeMillis()}")
        BatteryLogger.d(TAG, "Video path: ${appRepository.getVideoPath()}")
        BatteryLogger.d(TAG, "Applied animation URL: ${appRepository.getAppliedAnimationUrl()}")
        BatteryLogger.d(TAG, "Applied animation: ${animationRepository.getApplied()}")
        BatteryLogger.d(TAG, "=====================================")
    }

    override fun onDestroy() {
        BatteryLogger.i(TAG, "ChargingOverlayService destroyed")
        super.onDestroy()
        try {
            unregisterReceiver(batteryReceiver)
        } catch (e: Exception) {
            BatteryLogger.w(TAG, "Error unregistering battery receiver", e)
        }
    }

    override fun onBind(intent: Intent?): IBinder? = null

    companion object {
        private const val TAG = "CHARGING_OVERLAY"
        private const val NOTIFICATION_ID = 1001
    }
}